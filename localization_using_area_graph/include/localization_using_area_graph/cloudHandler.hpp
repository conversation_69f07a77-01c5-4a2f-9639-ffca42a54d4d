/**
 * @file cloudHandler.hpp
 * <AUTHOR> (ROS2 port)
 *         <PERSON><PERSON> (original ROS1 implementation)
 *         <PERSON><PERSON><PERSON> (original ROS1 implementation)
 * @brief Point cloud processing and localization using Area Graph map representation
 * @version 0.1
 * @date 2024-12-02
 *
 */
#pragma once
#ifndef _CLOUD_HANDLER_HPP_
#define _CLOUD_HANDLER_HPP_

#include "utility.hpp"
#include "cloudBase.hpp"
#include "cloudInitializer.hpp"
#include "odom_fusion.hpp"

#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/point_cloud2.hpp"
#include "sensor_msgs/msg/point_cloud.hpp"
#include "nav_msgs/msg/odometry.hpp"
#include "geometry_msgs/msg/pose_with_covariance_stamped.hpp"

// Message filters for ROS2
#include "message_filters/subscriber.h"
#include "message_filters/synchronizer.h"
// 并没有直接使用消息同步机制，允许各个消息独立处理
#include "message_filters/sync_policies/approximate_time.h"

class CloudHandler : public CloudBase {
public:
    // ROS2 订阅器
    rclcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr subLaserCloud;    // 订阅激光点云数据
    rclcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr subInitialGuess;  // 订阅初始位姿猜测
    rclcpp::Subscription<geometry_msgs::msg::PoseWithCovarianceStamped>::SharedPtr subManualInitialPose;  // 订阅手动设置的初始位姿

    // ROS2 发布器
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr pubRobotPose;      // 发布机器人位姿

    // cloudInitializer时在CloudHandler类构造时就已经被实例化了的
    std::shared_ptr<CloudInitializer> cloudInitializer;  // 使用智能指针管理云初始化器对象

    // 地图相关索引
    int insideAreaStartIndex;  // 地图点云索引
    int insideAreaID;          // AG索引数据结构

    // 处理变量
    std::vector<bool> vbHistogramRemain;  // 直方图剩余标志
    std::chrono::steady_clock::time_point sumFrameRunTime; // 使用ROS2时间，累计帧运行时间
    int numofFrame;       // 帧数
    bool hasGlobalPoseEstimate;    // 是否已从全局定位获得位姿估计
    bool hasManualInitialPose;     // 是否有手动设置的初始位姿
    int globalImgTimes;   // 全局图像次数

    explicit CloudHandler();  // 显式构造函数
    ~CloudHandler() override = default;  // 默认析构函数

    // 点云处理方法
    void filterUsefulPoints();  // 过滤有用点
    void optimizationICP();     // ICP优化
    void showImg1line(const std::string& words);  // 显示一行图像

    // 获取机器人位姿 - 为Nav2接口添加
    Eigen::Matrix4f getRobotPose() const { return robotPose; }  // 返回当前机器人位姿

    // 设置手动初始位姿 - 为Nav2接口添加
    void setManualInitialPose(double yaw, const Eigen::Vector3f& position);  // 设置手动初始位姿

    // 地图和直方图处理
    void mergeMapHistogram();  // 合并地图直方图
    double corridornessDSRate(double maxPercentage);  // 计算走廊度下采样率
    void gettingInsideWhichArea();  // 确定所在区域

    // 点云检查方法
    bool checkWholeMap(int pc_index,
                      const pcl::PointXYZI& PCPoint,
                      double &map1x,
                      double &map1y,
                      double &map2x,
                      double &map2y,
                      double &intersectionx,
                      double &intersectiony);  // 检查整个地图

    // 重写CloudBase方法
    void calClosestMapPoint(int inside_index) override;  // 计算最近地图点
    bool checkMap(int ring,
                 int horizonIndex,
                 int &last_index,
                 double &minDist,
                 int inside_index) override;  // 检查地图
    void allocateMemory() override;  // 分配内存
    void resetParameters() override;  // 重置参数

private:
    // ========== 运动历史管理相关 ==========
    std::deque<Eigen::Matrix4f> pose_history_;     // 位姿历史记录
    std::deque<rclcpp::Time> time_history_;        // 时间历史记录
    static constexpr size_t MAX_HISTORY_SIZE = 10; // 最大历史记录大小
    
    // 运动预测相关
    Eigen::Vector3d linear_velocity_;              // 线性速度估计
    double angular_velocity_;                      // 角速度估计
    bool motion_initialized_;                      // 运动模型是否已初始化

    // 速度平滑相关
    std::deque<Eigen::Vector3d> velocity_history_;     // 速度历史记录
    std::deque<double> angular_velocity_history_;      // 角速度历史记录
    static constexpr size_t MAX_VELOCITY_HISTORY_SIZE = 10;  // 最大速度历史大小
    bool velocity_history_initialized_;                // 速度历史是否已初始化

    // 速度平滑参数
    bool enable_velocity_smoothing_;               // 是否启用速度平滑
    size_t velocity_window_size_;                  // 速度计算窗口大小
    double velocity_weight_decay_;                 // 时间权重衰减因子
    double velocity_filter_alpha_;                 // 低通滤波系数

    // ========== 独立运动估计相关 ==========
    // 里程计独立运动估计
    std::deque<nav_msgs::msg::Odometry> odom_history_;          // 里程计历史记录
    static constexpr size_t MAX_ODOM_HISTORY_SIZE = 20;        // 最大里程计历史大小
    bool independent_motion_initialized_;                       // 独立运动估计是否初始化
    Eigen::Vector3d odom_linear_velocity_;                      // 里程计线性速度
    double odom_angular_velocity_;                              // 里程计角速度
    rclcpp::Time last_odom_time_;                              // 最后里程计时间
    Eigen::Matrix4f last_odom_pose_;                           // 最后里程计位姿

    // 独立运动估计参数
    bool enable_independent_motion_estimation_;                 // 是否启用独立运动估计
    double odom_velocity_smoothing_factor_;                     // 里程计速度平滑因子
    double max_odom_age_;                                       // 最大里程计数据年龄
    
    // ICP鲁棒性相关
    std::vector<Eigen::Matrix4f> backup_poses_;    // 备用位姿
    int retry_count_;                              // 重试计数
    static constexpr int MAX_RETRIES = 3;          // 最大重试次数

    // ========== 运动预测安全参数 ==========
    bool enable_motion_prediction_;          // 是否启用运动预测（主开关）
    bool conservative_mode_;                 // 保守模式（优先使用上一帧位姿）
    double max_prediction_distance_;         // 运动预测最大允许距离(米)
    double max_velocity_change_;             // 最大速度变化阈值
    size_t min_history_for_prediction_;      // 运动预测所需最小历史数量
    double motion_prediction_confidence_;    // 运动预测置信度阈值

    // ========== 里程计融合相关 ==========
    std::unique_ptr<agloc_fusion::OdomFusion> odom_fusion_;  // 里程计融合模块
    rclcpp::Subscription<nav_msgs::msg::Odometry>::SharedPtr odom_sub_;  // 里程计订阅器
    rclcpp::Publisher<geometry_msgs::msg::PoseStamped>::SharedPtr predicted_pose_pub_;  // 预测位姿发布器

    // 融合状态变量
    Eigen::Matrix4f last_fused_pose_;           // 上一次融合后的位姿
    rclcpp::Time last_fusion_time_;             // 上一次融合的时间
    bool fusion_initialized_;                   // 融合模块是否已初始化
    double last_icp_score_;                     // 上一次ICP得分

    // 回调方法
    void cloudHandlerCB(const sensor_msgs::msg::PointCloud2::SharedPtr laserCloudMsg);  // 处理接收到的点云数据
    void setInitialGuessFlag(const sensor_msgs::msg::PointCloud2::SharedPtr laserCloudMsg);  // 设置初始猜测标志
    void manualInitialPoseCB(const std::shared_ptr<geometry_msgs::msg::PoseWithCovarianceStamped> poseMsg);  // 处理手动设置的初始位姿
    void odomCallback(const nav_msgs::msg::Odometry::SharedPtr odom_msg);  // 里程计回调函数

    // ========== 运动历史管理方法 ==========
    void updatePoseHistory(const Eigen::Matrix4f& pose, const rclcpp::Time& time);  // 更新位姿历史
    Eigen::Matrix4f predictPoseFromMotion(const rclcpp::Time& target_time);         // 基于运动历史预测位姿
    void computeVelocities();                                                       // 计算速度估计
    void initializeMotionTracking();                                               // 初始化运动跟踪
    bool isMotionPredictionValid();                                                 // 检查运动预测是否有效

    // ========== 速度平滑方法 ==========
    void computeSmoothedVelocities();                                              // 计算平滑速度
    void computeSimpleVelocities();                                                // 简单速度计算（回退方法）
    double computeInstantAngularVelocity(const Eigen::Matrix4f& current_pose,      // 计算瞬时角速度
                                        const Eigen::Matrix4f& previous_pose,
                                        double dt);
    void updateVelocityHistory();                                                  // 更新速度历史

    // ========== 智能运动分析方法 ==========
    double computeVelocityStandardDeviation();                                    // 计算速度标准差
    double computeMotionConsistencyScore();                                       // 计算运动一致性得分
    double computeVelocityConsistency();                                          // 计算速度一致性
    double computeDirectionConsistency();                                         // 计算方向一致性
    double computeTrajectorySmoothness();                                         // 计算轨迹平滑性
    double computeAdaptiveMaxPredictionTime();                                   // 计算自适应最大预测时间

    // ========== 独立运动估计方法 ==========
    void initializeIndependentMotionEstimation();                               // 初始化独立运动估计
    void updateOdomHistory(const nav_msgs::msg::Odometry::SharedPtr odom_msg);  // 更新里程计历史
    void computeOdomVelocities();                                               // 计算里程计速度
    Eigen::Matrix4f predictPoseFromOdom(const rclcpp::Time& target_time);      // 基于里程计预测位姿
    bool isOdomMotionValid();                                                   // 检查里程计运动是否有效
    Eigen::Matrix4f getIndependentInitialPose(const rclcpp::Time& target_time); // 获取独立初始位姿

    // ========== ICP初始化增强方法 ==========
    std::vector<Eigen::Matrix4f> generateMultipleInitialGuesses(const rclcpp::Time& target_time);  // 生成多个初始位姿猜测
    Eigen::Matrix4f selectBestInitialGuess(const std::vector<Eigen::Matrix4f>& candidates);        // 选择最佳初始位姿
    double evaluateInitialGuess(const Eigen::Matrix4f& pose_guess);                               // 评估初始位姿的质量
    Eigen::Matrix4f getICPInitialPose(const rclcpp::Time& target_time);                          // 获取ICP的初始位姿

    // ========== 里程计融合方法 ==========
    void initializeOdomFusion();                                          // 初始化里程计融合模块
    Eigen::Matrix4f applyOdomFusion(const Eigen::Matrix4f& icp_pose,     // 应用里程计融合
                                   double icp_score,
                                   const rclcpp::Time& timestamp);
    double computeICPScore();                                             // 计算ICP匹配得分
    void publishPredictedPose(const Eigen::Matrix4f& predicted_pose,     // 发布预测位姿
                             const rclcpp::Time& timestamp);
    void publishRobotPathGeo(const geometry_msgs::msg::PoseStamped& pose_stamped);  // 发布地理坐标路径

    // 初始化发布器和订阅器
    void initializePublishers() {
        // 创建机器人位姿发布者
        pubRobotPose = create_publisher<geometry_msgs::msg::PoseStamped>(
            "/cloud_handler/pose", rclcpp::QoS(1).reliable());

        // 如果启用位姿预测发布，创建预测位姿发布者
        if (publish_prediction) {
            predicted_pose_pub_ = create_publisher<geometry_msgs::msg::PoseStamped>(
                "/cloud_handler/predicted_pose", rclcpp::QoS(1).reliable());
        }
    }

    void initializeSubscribers() {
        auto qos = rclcpp::QoS(rclcpp::KeepLast(10));

        // 订阅点云话题
        subLaserCloud = this->create_subscription<sensor_msgs::msg::PointCloud2>(
            pointCloudTopic, qos,
            std::bind(&CloudHandler::cloudHandlerCB, this, std::placeholders::_1));

        // 订阅初始猜测粒子，一旦检测到生成的粒子，则使得标识符 hasGlobalPoseEstimate == True
        subInitialGuess = this->create_subscription<sensor_msgs::msg::PointCloud2>(
            "/particles_for_init", qos,
            std::bind(&CloudHandler::setInitialGuessFlag, this, std::placeholders::_1));

        // 订阅手动设置的初始位姿话题
        subManualInitialPose = this->create_subscription<geometry_msgs::msg::PoseWithCovarianceStamped>(
            "/initialpose_agloc", qos,
            std::bind(&CloudHandler::manualInitialPoseCB, this, std::placeholders::_1));

        // 如果启用里程计融合，订阅里程计话题
        if (enable_odom_fusion) {
            odom_sub_ = this->create_subscription<nav_msgs::msg::Odometry>(
                odom_topic, qos,
                std::bind(&CloudHandler::odomCallback, this, std::placeholders::_1));
        }
    }

    // 初始化变量
    void initializeVariables() {
        // 初始化时间跟踪
        sumFrameRunTime = std::chrono::steady_clock::now();
        insideAreaStartIndex = 0;  // 内部区域起始索引
        insideAreaID = 0;          // 内部区域ID
        numofFrame = 0;            // 帧数
        hasGlobalPoseEstimate = false;      // 是否已从全局定位获得位姿估计
        hasManualInitialPose = false;       // 是否有手动设置的初始位姿
        globalImgTimes = 0;        // 全局图像次数

        // 初始化里程计融合相关变量
        fusion_initialized_ = false;
        last_fused_pose_ = Eigen::Matrix4f::Identity();
        last_fusion_time_ = this->now();
        last_icp_score_ = 0.0;

        // 如果启用里程计融合，初始化融合模块
        if (enable_odom_fusion) {
            initializeOdomFusion();
        }
    }

protected:
    // Add any protected members if needed
};

#endif // _CLOUD_HANDLER_HPP_