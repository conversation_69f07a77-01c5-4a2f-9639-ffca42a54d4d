#!/usr/bin/env python3
"""
AGLoc鲁棒性测试框架
用于定量评估位姿跟踪的鲁棒性和性能
"""

import os
import sys
import json
import time
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import subprocess
import signal
import threading
import rclpy
from rclpy.node import Node
from geometry_msgs.msg import PoseStamped
from nav_msgs.msg import Path
from std_msgs.msg import Header

class RobustnessTestNode(Node):
    """ROS2节点，用于监控和评估AGLoc性能"""
    
    def __init__(self):
        super().__init__('robustness_test_node')
        
        # 测试参数
        self.test_start_time = time.time()
        self.poses = []
        self.pose_times = []
        self.tracking_lost_events = []
        self.icp_scores = []
        self.position_drifts = []
        self.last_pose = None
        self.lost_tracking = False
        self.lost_tracking_duration = 0.0
        self.lost_tracking_start = None
        
        # 统计数据
        self.total_poses = 0
        self.successful_poses = 0
        self.max_position_jump = 0.0
        self.avg_position_drift = 0.0
        
        # ROS订阅者
        self.pose_sub = self.create_subscription(
            PoseStamped,
            '/robot_pose',
            self.pose_callback,
            10
        )
        
        self.path_sub = self.create_subscription(
            Path,
            '/robot_path',
            self.path_callback,
            10
        )
        
        # 定时器：定期检查系统状态
        self.status_timer = self.create_timer(1.0, self.check_system_status)
        
        self.get_logger().info("🔍 鲁棒性测试节点已启动")
    
    def pose_callback(self, msg: PoseStamped):
        """位姿回调函数"""
        current_time = time.time()
        current_pose = np.array([
            msg.pose.position.x,
            msg.pose.position.y,
            msg.pose.position.z
        ])
        
        self.poses.append(current_pose)
        self.pose_times.append(current_time)
        self.total_poses += 1
        
        # 检查位姿跳跃
        if self.last_pose is not None:
            position_jump = np.linalg.norm(current_pose - self.last_pose)
            self.max_position_jump = max(self.max_position_jump, position_jump)
            
            # 检测是否失去跟踪（位姿跳跃过大）
            if position_jump > 2.0:  # 2米阈值
                if not self.lost_tracking:
                    self.lost_tracking = True
                    self.lost_tracking_start = current_time
                    self.get_logger().warn(f"⚠️ 检测到跟踪丢失，位姿跳跃: {position_jump:.3f}米")
            else:
                if self.lost_tracking:
                    # 恢复跟踪
                    lost_duration = current_time - self.lost_tracking_start
                    self.lost_tracking_duration += lost_duration
                    self.tracking_lost_events.append({
                        'start_time': self.lost_tracking_start,
                        'duration': lost_duration,
                        'recovery_time': current_time
                    })
                    self.lost_tracking = False
                    self.get_logger().info(f"✅ 跟踪已恢复，丢失持续时间: {lost_duration:.3f}秒")
                
                self.successful_poses += 1
        
        self.last_pose = current_pose
    
    def path_callback(self, msg: Path):
        """路径回调函数"""
        if len(msg.poses) >= 2:
            # 计算路径平滑度
            positions = np.array([[p.pose.position.x, p.pose.position.y] 
                                for p in msg.poses[-10:]])  # 最近10个位姿
            if len(positions) >= 3:
                # 计算平均位置漂移
                drifts = [np.linalg.norm(positions[i+1] - positions[i]) 
                         for i in range(len(positions)-1)]
                self.avg_position_drift = np.mean(drifts)
    
    def check_system_status(self):
        """定期检查系统状态"""
        current_time = time.time()
        test_duration = current_time - self.test_start_time
        
        # 检查是否长时间没有收到位姿消息
        if self.pose_times and (current_time - self.pose_times[-1]) > 5.0:
            if not self.lost_tracking:
                self.lost_tracking = True
                self.lost_tracking_start = current_time
                self.get_logger().error("🚨 系统停止发布位姿，可能已崩溃")
        
        # 输出统计信息
        if int(test_duration) % 30 == 0 and test_duration > 0:  # 每30秒输出一次
            self.print_statistics()
    
    def print_statistics(self):
        """打印当前统计信息"""
        test_duration = time.time() - self.test_start_time
        success_rate = (self.successful_poses / max(1, self.total_poses)) * 100
        
        self.get_logger().info(f"""
📊 鲁棒性测试统计 (测试时长: {test_duration:.1f}秒):
   总位姿数: {self.total_poses}
   成功位姿数: {self.successful_poses}
   成功率: {success_rate:.2f}%
   最大位姿跳跃: {self.max_position_jump:.3f}米
   平均位置漂移: {self.avg_position_drift:.4f}米
   跟踪丢失次数: {len(self.tracking_lost_events)}
   总丢失时长: {self.lost_tracking_duration:.3f}秒
        """)
    
    def get_test_results(self) -> Dict:
        """获取测试结果"""
        test_duration = time.time() - self.test_start_time
        success_rate = (self.successful_poses / max(1, self.total_poses)) * 100
        
        return {
            'test_duration': test_duration,
            'total_poses': self.total_poses,
            'successful_poses': self.successful_poses,
            'success_rate': success_rate,
            'max_position_jump': self.max_position_jump,
            'avg_position_drift': self.avg_position_drift,
            'tracking_lost_events': len(self.tracking_lost_events),
            'total_lost_duration': self.lost_tracking_duration,
            'poses': [pose.tolist() for pose in self.poses],
            'pose_times': self.pose_times,
            'lost_events': self.tracking_lost_events
        }


class RobustnessTestFramework:
    """AGLoc鲁棒性测试框架"""
    
    def __init__(self, workspace_path: str):
        self.workspace_path = Path(workspace_path)
        self.results_dir = self.workspace_path / "test_results"
        self.results_dir.mkdir(exist_ok=True)
        
        # 测试配置
        self.test_configs = [
            {"name": "原始系统", "enable_motion_prediction": False},
            {"name": "保守模式", "enable_motion_prediction": True, "conservative_mode": True},
            {"name": "完整模式", "enable_motion_prediction": True, "conservative_mode": False},
        ]
        
        self.rosbag_files = []
        self.test_results = {}
    
    def find_rosbag_files(self, bag_dir: str) -> List[str]:
        """查找测试用的rosbag文件"""
        bag_path = Path(bag_dir)
        if not bag_path.exists():
            print(f"❌ rosbag目录不存在: {bag_dir}")
            return []
        
        # 查找.mcap或.bag文件
        bag_files = list(bag_path.glob("*.mcap")) + list(bag_path.glob("*.bag"))
        
        if not bag_files:
            print(f"❌ 在 {bag_dir} 中未找到rosbag文件")
            return []
        
        print(f"✅ 找到 {len(bag_files)} 个rosbag文件:")
        for bag_file in bag_files:
            print(f"   - {bag_file.name}")
        
        return [str(f) for f in bag_files]
    
    def modify_config(self, config: Dict):
        """修改params.yaml配置"""
        params_file = self.workspace_path / "src/localization_using_area_graph/config/params.yaml"
        
        if not params_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {params_file}")
        
        # 读取原始配置
        with open(params_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修改配置参数
        for key, value in config.items():
            if key == "name":
                continue
            
            # 查找并替换参数
            if key == "enable_motion_prediction":
                content = self._replace_param(content, key, value)
            elif key == "conservative_mode":
                content = self._replace_param(content, key, value)
        
        # 写回配置文件
        with open(params_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ 配置已更新为: {config['name']}")
    
    def _replace_param(self, content: str, param_name: str, value) -> str:
        """替换配置参数值"""
        import re
        
        # 构建正则表达式模式
        pattern = f"({param_name}:\\s*)(true|false|\\d+\\.?\\d*)"
        replacement = f"\\g<1>{str(value).lower()}"
        
        return re.sub(pattern, replacement, content)
    
    def run_single_test(self, config: Dict, bag_file: str, test_duration: int = 60) -> Dict:
        """运行单个测试"""
        print(f"\n🔬 开始测试: {config['name']} 使用 {Path(bag_file).name}")
        
        # 修改配置
        self.modify_config(config)
        
        # 编译代码
        print("🔨 编译代码...")
        compile_result = subprocess.run(
            ["colcon", "build", "--symlink-install", "--packages-select", "localization_using_area_graph"],
            cwd=self.workspace_path,
            capture_output=True,
            text=True
        )
        
        if compile_result.returncode != 0:
            print(f"❌ 编译失败: {compile_result.stderr}")
            return {"error": "compilation_failed", "stderr": compile_result.stderr}
        
        # 初始化ROS2
        rclpy.init()
        test_node = RobustnessTestNode()
        
        # 启动AGLoc系统
        print("🚀 启动AGLoc系统...")
        env = os.environ.copy()
        env["ROS_DOMAIN_ID"] = "42"  # 使用独立的domain避免干扰
        
        agloc_process = subprocess.Popen([
            "ros2", "launch", "localization_using_area_graph", "run.launch.py"
        ], 
        cwd=self.workspace_path,
        env=env,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
        )
        
        # 等待系统启动
        time.sleep(5)
        
        # 播放rosbag
        print(f"📼 播放rosbag: {Path(bag_file).name}")
        bag_process = subprocess.Popen([
            "ros2", "bag", "play", bag_file, "--rate", "1.0"
        ], env=env)
        
        # 运行测试
        start_time = time.time()
        try:
            while (time.time() - start_time) < test_duration:
                rclpy.spin_once(test_node, timeout_sec=0.1)
                
                # 检查进程状态
                if agloc_process.poll() is not None:
                    print("⚠️ AGLoc进程提前退出")
                    break
                    
                if bag_process.poll() is not None:
                    print("✅ rosbag播放完成")
                    break
        
        except KeyboardInterrupt:
            print("⏹️ 测试被用户中断")
        
        finally:
            # 清理进程
            try:
                bag_process.terminate()
                bag_process.wait(timeout=5)
            except:
                bag_process.kill()
            
            try:
                agloc_process.terminate()
                agloc_process.wait(timeout=5)
            except:
                agloc_process.kill()
            
            # 获取测试结果
            results = test_node.get_test_results()
            test_node.destroy_node()
            rclpy.shutdown()
        
        print(f"✅ 测试完成: {config['name']}")
        return results
    
    def run_batch_tests(self, bag_files: List[str], test_duration: int = 60):
        """运行批量测试"""
        print(f"\n🎯 开始批量测试，共 {len(self.test_configs)} 个配置 × {len(bag_files)} 个rosbag")
        
        all_results = {}
        
        for config in self.test_configs:
            config_results = {}
            
            for bag_file in bag_files:
                bag_name = Path(bag_file).stem
                
                try:
                    result = self.run_single_test(config, bag_file, test_duration)
                    config_results[bag_name] = result
                    
                    # 保存中间结果
                    self.save_results({config['name']: config_results})
                    
                except Exception as e:
                    print(f"❌ 测试失败: {config['name']} - {bag_name}: {e}")
                    config_results[bag_name] = {"error": str(e)}
            
            all_results[config['name']] = config_results
        
        self.test_results = all_results
        return all_results
    
    def save_results(self, results: Dict):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = self.results_dir / f"robustness_test_{timestamp}.json"
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        print(f"💾 结果已保存到: {results_file}")
        
        # 生成报告
        self.generate_report(results, timestamp)
    
    def generate_report(self, results: Dict, timestamp: str):
        """生成测试报告"""
        report_file = self.results_dir / f"robustness_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"# AGLoc鲁棒性测试报告\n\n")
            f.write(f"**测试时间**: {timestamp}\n\n")
            
            # 创建统计表格
            f.write("## 测试结果统计\n\n")
            f.write("| 配置 | rosbag | 成功率(%) | 最大跳跃(m) | 平均漂移(m) | 丢失次数 | 丢失时长(s) |\n")
            f.write("|------|---------|-----------|-------------|-------------|----------|-------------|\n")
            
            for config_name, config_results in results.items():
                for bag_name, result in config_results.items():
                    if "error" not in result:
                        f.write(f"| {config_name} | {bag_name} | "
                               f"{result['success_rate']:.2f} | "
                               f"{result['max_position_jump']:.3f} | "
                               f"{result['avg_position_drift']:.4f} | "
                               f"{result['tracking_lost_events']} | "
                               f"{result['total_lost_duration']:.3f} |\n")
                    else:
                        f.write(f"| {config_name} | {bag_name} | ERROR | - | - | - | - |\n")
            
            # 添加详细分析
            f.write("\n## 详细分析\n\n")
            
            for config_name, config_results in results.items():
                f.write(f"### {config_name}\n\n")
                
                success_rates = []
                position_jumps = []
                lost_events = []
                
                for bag_name, result in config_results.items():
                    if "error" not in result:
                        success_rates.append(result['success_rate'])
                        position_jumps.append(result['max_position_jump'])
                        lost_events.append(result['tracking_lost_events'])
                        
                        f.write(f"**{bag_name}**:\n")
                        f.write(f"- 成功率: {result['success_rate']:.2f}%\n")
                        f.write(f"- 最大位姿跳跃: {result['max_position_jump']:.3f}米\n")
                        f.write(f"- 跟踪丢失次数: {result['tracking_lost_events']}\n\n")
                
                if success_rates:
                    f.write(f"**{config_name} 汇总**:\n")
                    f.write(f"- 平均成功率: {np.mean(success_rates):.2f}%\n")
                    f.write(f"- 平均最大跳跃: {np.mean(position_jumps):.3f}米\n")
                    f.write(f"- 平均丢失次数: {np.mean(lost_events):.1f}\n\n")
        
        print(f"📊 报告已生成: {report_file}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AGLoc鲁棒性测试框架")
    parser.add_argument("--workspace", default="/home/<USER>/AGLoc_ws", 
                       help="AGLoc工作空间路径")
    parser.add_argument("--bags", required=True,
                       help="rosbag文件目录路径")
    parser.add_argument("--duration", type=int, default=60,
                       help="每个测试的持续时间(秒)")
    
    args = parser.parse_args()
    
    # 创建测试框架
    framework = RobustnessTestFramework(args.workspace)
    
    # 查找rosbag文件
    bag_files = framework.find_rosbag_files(args.bags)
    if not bag_files:
        print("❌ 没有找到可用的rosbag文件")
        return
    
    # 运行批量测试
    results = framework.run_batch_tests(bag_files, args.duration)
    
    # 保存最终结果
    framework.save_results(results)
    
    print("\n🎉 批量测试完成！")
    print(f"📁 结果保存在: {framework.results_dir}")


if __name__ == "__main__":
    main() 