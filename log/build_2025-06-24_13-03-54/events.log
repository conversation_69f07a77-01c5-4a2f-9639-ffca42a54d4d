[0.000000] (-) TimerEvent: {}
[0.000456] (-) JobUnselected: {'identifier': 'area_graph_data_parser'}
[0.000507] (-) JobUnselected: {'identifier': 'hesai_amcl'}
[0.000564] (-) JobUnselected: {'identifier': 'nav2_amcl'}
[0.000584] (-) JobUnselected: {'identifier': 'rss'}
[0.000604] (-) JobUnselected: {'identifier': 'wifi_dummy_node'}
[0.000627] (-) JobUnselected: {'identifier': 'wifi_loc'}
[0.000680] (localization_using_area_graph) JobQueued: {'identifier': 'localization_using_area_graph', 'dependencies': OrderedDict([('area_graph_data_parser', '/home/<USER>/AGLoc_ws/src/install/area_graph_data_parser'), ('rss', '/home/<USER>/AGLoc_ws/src/install/rss')])}
[0.000724] (localization_using_area_graph) JobStarted: {'identifier': 'localization_using_area_graph'}
[0.010322] (localization_using_area_graph) JobEnded: {'identifier': 'localization_using_area_graph', 'rc': 1}
[0.020880] (-) EventReactorShutdown: {}
