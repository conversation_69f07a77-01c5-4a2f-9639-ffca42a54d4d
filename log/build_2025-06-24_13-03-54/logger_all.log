[0.075s] DEBUG:colcon:Command line arguments: ['/home/<USER>/anaconda3/envs/ros2_env/bin/colcon', 'build', '--symlink-install', '--packages-select', 'localization_using_area_graph']
[0.075s] DEBUG:colcon:Parsed command line arguments: Namespace(log_base=None, log_level=None, verb_name='build', build_base='build', install_base='install', merge_install=False, symlink_install=True, test_result_base=None, continue_on_error=False, executor='parallel', parallel_workers=16, event_handlers=None, ignore_user_meta=False, metas=['./colcon.meta'], base_paths=['.'], packages_ignore=None, packages_ignore_regex=None, paths=None, packages_up_to=None, packages_up_to_regex=None, packages_above=None, packages_above_and_dependencies=None, packages_above_depth=None, packages_select_by_dep=None, packages_skip_by_dep=None, packages_skip_up_to=None, packages_select_build_failed=False, packages_skip_build_finished=False, packages_select_test_failures=False, packages_skip_test_passed=False, packages_select=['localization_using_area_graph'], packages_skip=None, packages_select_regex=None, packages_skip_regex=None, packages_start=None, packages_end=None, cmake_args=None, cmake_target=None, cmake_target_skip_unavailable=False, cmake_clean_cache=False, cmake_clean_first=False, cmake_force_configure=False, ament_cmake_args=None, catkin_cmake_args=None, catkin_skip_building_tests=False, verb_parser=<colcon_defaults.argument_parser.defaults.DefaultArgumentsDecorator object at 0x7e0b8240b0a0>, verb_extension=<colcon_core.verb.build.BuildVerb object at 0x7e0b82550550>, main=<bound method BuildVerb.main of <colcon_core.verb.build.BuildVerb object at 0x7e0b82550550>>)
[0.097s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) check parameters
[0.097s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) check parameters
[0.097s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) check parameters
[0.097s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) check parameters
[0.097s] Level 1:colcon.colcon_core.package_discovery:discover_packages(colcon_meta) discover
[0.097s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) discover
[0.097s] INFO:colcon.colcon_core.package_discovery:Crawling recursively for packages in '/home/<USER>/AGLoc_ws/src'
[0.097s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ignore', 'ignore_ament_install']
[0.097s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore'
[0.097s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ignore_ament_install'
[0.097s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_pkg']
[0.097s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_pkg'
[0.097s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['colcon_meta']
[0.097s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'colcon_meta'
[0.097s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['ros']
[0.097s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'ros'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['cmake', 'python']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'cmake'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extensions ['python_setup_py']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(.) by extension 'python_setup_py'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extensions ['ignore', 'ignore_ament_install']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extension 'ignore'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extension 'ignore_ament_install'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extensions ['colcon_pkg']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extension 'colcon_pkg'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extensions ['colcon_meta']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extension 'colcon_meta'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extensions ['ros']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extension 'ros'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extensions ['cmake', 'python']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extension 'cmake'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extension 'python'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extensions ['python_setup_py']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(__pycache__) by extension 'python_setup_py'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(area_graph_data_parser) by extensions ['ignore', 'ignore_ament_install']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(area_graph_data_parser) by extension 'ignore'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(area_graph_data_parser) by extension 'ignore_ament_install'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(area_graph_data_parser) by extensions ['colcon_pkg']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(area_graph_data_parser) by extension 'colcon_pkg'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(area_graph_data_parser) by extensions ['colcon_meta']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(area_graph_data_parser) by extension 'colcon_meta'
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(area_graph_data_parser) by extensions ['ros']
[0.111s] Level 1:colcon.colcon_core.package_identification:_identify(area_graph_data_parser) by extension 'ros'
[0.113s] DEBUG:colcon.colcon_core.package_identification:Package 'area_graph_data_parser' with type 'ros.ament_cmake' and name 'area_graph_data_parser'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extensions ['ignore', 'ignore_ament_install']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(build) by extension 'ignore'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(build) ignored
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(hesai_amcl) by extensions ['ignore', 'ignore_ament_install']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(hesai_amcl) by extension 'ignore'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(hesai_amcl) by extension 'ignore_ament_install'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(hesai_amcl) by extensions ['colcon_pkg']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(hesai_amcl) by extension 'colcon_pkg'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(hesai_amcl) by extensions ['colcon_meta']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(hesai_amcl) by extension 'colcon_meta'
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(hesai_amcl) by extensions ['ros']
[0.113s] Level 1:colcon.colcon_core.package_identification:_identify(hesai_amcl) by extension 'ros'
[0.114s] DEBUG:colcon.colcon_core.package_identification:Package 'hesai_amcl' with type 'ros.ament_cmake' and name 'hesai_amcl'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extensions ['ignore', 'ignore_ament_install']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(install) by extension 'ignore'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(install) ignored
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(localization_using_area_graph) by extensions ['ignore', 'ignore_ament_install']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(localization_using_area_graph) by extension 'ignore'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(localization_using_area_graph) by extension 'ignore_ament_install'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(localization_using_area_graph) by extensions ['colcon_pkg']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(localization_using_area_graph) by extension 'colcon_pkg'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(localization_using_area_graph) by extensions ['colcon_meta']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(localization_using_area_graph) by extension 'colcon_meta'
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(localization_using_area_graph) by extensions ['ros']
[0.114s] Level 1:colcon.colcon_core.package_identification:_identify(localization_using_area_graph) by extension 'ros'
[0.116s] DEBUG:colcon.colcon_core.package_identification:Package 'localization_using_area_graph' with type 'ros.ament_cmake' and name 'localization_using_area_graph'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extensions ['ignore', 'ignore_ament_install']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(log) by extension 'ignore'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(log) ignored
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(nav2_amcl) by extensions ['ignore', 'ignore_ament_install']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(nav2_amcl) by extension 'ignore'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(nav2_amcl) by extension 'ignore_ament_install'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(nav2_amcl) by extensions ['colcon_pkg']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(nav2_amcl) by extension 'colcon_pkg'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(nav2_amcl) by extensions ['colcon_meta']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(nav2_amcl) by extension 'colcon_meta'
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(nav2_amcl) by extensions ['ros']
[0.116s] Level 1:colcon.colcon_core.package_identification:_identify(nav2_amcl) by extension 'ros'
[0.117s] DEBUG:colcon.colcon_core.package_identification:Package 'nav2_amcl' with type 'ros.ament_cmake' and name 'nav2_amcl'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extensions ['ignore', 'ignore_ament_install']
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extension 'ignore'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extension 'ignore_ament_install'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extensions ['colcon_pkg']
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extension 'colcon_pkg'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extensions ['colcon_meta']
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extension 'colcon_meta'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extensions ['ros']
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extension 'ros'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extensions ['cmake', 'python']
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extension 'cmake'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extension 'python'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extensions ['python_setup_py']
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings) by extension 'python_setup_py'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extensions ['ignore', 'ignore_ament_install']
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extension 'ignore'
[0.117s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extension 'ignore_ament_install'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extensions ['colcon_pkg']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extension 'colcon_pkg'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extensions ['colcon_meta']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extension 'colcon_meta'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extensions ['ros']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extension 'ros'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extensions ['cmake', 'python']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extension 'cmake'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extension 'python'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extensions ['python_setup_py']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/agloc_recording_20250623_141313) by extension 'python_setup_py'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extensions ['ignore', 'ignore_ament_install']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extension 'ignore'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extension 'ignore_ament_install'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extensions ['colcon_pkg']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extension 'colcon_pkg'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extensions ['colcon_meta']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extension 'colcon_meta'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extensions ['ros']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extension 'ros'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extensions ['cmake', 'python']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extension 'cmake'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extension 'python'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extensions ['python_setup_py']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(recordings/msg) by extension 'python_setup_py'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(rss) by extensions ['ignore', 'ignore_ament_install']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(rss) by extension 'ignore'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(rss) by extension 'ignore_ament_install'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(rss) by extensions ['colcon_pkg']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(rss) by extension 'colcon_pkg'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(rss) by extensions ['colcon_meta']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(rss) by extension 'colcon_meta'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(rss) by extensions ['ros']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(rss) by extension 'ros'
[0.118s] DEBUG:colcon.colcon_core.package_identification:Package 'rss' with type 'ros.ament_cmake' and name 'rss'
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_dummy_node) by extensions ['ignore', 'ignore_ament_install']
[0.118s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_dummy_node) by extension 'ignore'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_dummy_node) by extension 'ignore_ament_install'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_dummy_node) by extensions ['colcon_pkg']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_dummy_node) by extension 'colcon_pkg'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_dummy_node) by extensions ['colcon_meta']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_dummy_node) by extension 'colcon_meta'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_dummy_node) by extensions ['ros']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_dummy_node) by extension 'ros'
[0.119s] DEBUG:colcon.colcon_core.package_identification:Package 'wifi_dummy_node' with type 'ros.ament_cmake' and name 'wifi_dummy_node'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_loc) by extensions ['ignore', 'ignore_ament_install']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_loc) by extension 'ignore'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_loc) by extension 'ignore_ament_install'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_loc) by extensions ['colcon_pkg']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_loc) by extension 'colcon_pkg'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_loc) by extensions ['colcon_meta']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_loc) by extension 'colcon_meta'
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_loc) by extensions ['ros']
[0.119s] Level 1:colcon.colcon_core.package_identification:_identify(wifi_loc) by extension 'ros'
[0.120s] DEBUG:colcon.colcon_core.package_identification:Package 'wifi_loc' with type 'ros.ament_python' and name 'wifi_loc'
[0.120s] Level 1:colcon.colcon_core.package_discovery:discover_packages(recursive) using defaults
[0.120s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) discover
[0.120s] Level 1:colcon.colcon_core.package_discovery:discover_packages(ignore) using defaults
[0.120s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) discover
[0.120s] Level 1:colcon.colcon_core.package_discovery:discover_packages(path) using defaults
[0.131s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'area_graph_data_parser' in 'area_graph_data_parser'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'nav2_amcl' in 'nav2_amcl'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'rss' in 'rss'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'hesai_amcl' in 'hesai_amcl'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'wifi_dummy_node' in 'wifi_dummy_node'
[0.132s] INFO:colcon.colcon_core.package_selection:Skipping not selected package 'wifi_loc' in 'wifi_loc'
[0.132s] Level 5:colcon.colcon_core.verb:set package 'localization_using_area_graph' build argument 'cmake_args' from command line to 'None'
[0.132s] Level 5:colcon.colcon_core.verb:set package 'localization_using_area_graph' build argument 'cmake_target' from command line to 'None'
[0.132s] Level 5:colcon.colcon_core.verb:set package 'localization_using_area_graph' build argument 'cmake_target_skip_unavailable' from command line to 'False'
[0.132s] Level 5:colcon.colcon_core.verb:set package 'localization_using_area_graph' build argument 'cmake_clean_cache' from command line to 'False'
[0.132s] Level 5:colcon.colcon_core.verb:set package 'localization_using_area_graph' build argument 'cmake_clean_first' from command line to 'False'
[0.132s] Level 5:colcon.colcon_core.verb:set package 'localization_using_area_graph' build argument 'cmake_force_configure' from command line to 'False'
[0.132s] Level 5:colcon.colcon_core.verb:set package 'localization_using_area_graph' build argument 'ament_cmake_args' from command line to 'None'
[0.132s] Level 5:colcon.colcon_core.verb:set package 'localization_using_area_graph' build argument 'catkin_cmake_args' from command line to 'None'
[0.132s] Level 5:colcon.colcon_core.verb:set package 'localization_using_area_graph' build argument 'catkin_skip_building_tests' from command line to 'False'
[0.132s] DEBUG:colcon.colcon_core.verb:Building package 'localization_using_area_graph' with the following arguments: {'ament_cmake_args': None, 'build_base': '/home/<USER>/AGLoc_ws/src/build/localization_using_area_graph', 'catkin_cmake_args': None, 'catkin_skip_building_tests': False, 'cmake_args': None, 'cmake_clean_cache': False, 'cmake_clean_first': False, 'cmake_force_configure': False, 'cmake_target': None, 'cmake_target_skip_unavailable': False, 'install_base': '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph', 'merge_install': False, 'path': '/home/<USER>/AGLoc_ws/src/localization_using_area_graph', 'symlink_install': True, 'test_result_base': None}
[0.132s] INFO:colcon.colcon_core.executor:Executing jobs using 'parallel' executor
[0.133s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete
[0.133s] INFO:colcon.colcon_ros.task.ament_cmake.build:Building ROS package in '/home/<USER>/AGLoc_ws/src/localization_using_area_graph' with build type 'ament_cmake'
[0.133s] INFO:colcon.colcon_cmake.task.cmake.build:Building CMake package in '/home/<USER>/AGLoc_ws/src/localization_using_area_graph'
[0.134s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_core.shell.bat': Not used on non-Windows systems
[0.134s] INFO:colcon.colcon_core.shell:Skip shell extension 'powershell' for command environment: Not usable outside of PowerShell
[0.134s] DEBUG:colcon.colcon_core.shell:Skip shell extension 'dsv' for command environment
[0.136s] ERROR:colcon.colcon_cmake.task.cmake.build:Failed to find the following files:
- /home/<USER>/AGLoc_ws/src/install/area_graph_data_parser/share/area_graph_data_parser/package.sh
- /home/<USER>/AGLoc_ws/src/install/rss/share/rss/package.sh
Check that the following packages have been built:
- area_graph_data_parser
- rss
[0.136s] Level 1:colcon.colcon_core.environment:create_environment_scripts_only(localization_using_area_graph)
[0.137s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph' for CMake module files
[0.137s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph' for CMake config files
[0.138s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph/bin'
[0.138s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph/lib/pkgconfig/localization_using_area_graph.pc'
[0.140s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph/lib/python3.10/site-packages'
[0.140s] Level 1:colcon.colcon_core.environment:checking '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph/bin'
[0.140s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph/share/localization_using_area_graph/package.ps1'
[0.141s] INFO:colcon.colcon_core.shell:Creating package descriptor '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph/share/localization_using_area_graph/package.dsv'
[0.142s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph/share/localization_using_area_graph/package.sh'
[0.142s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph/share/localization_using_area_graph/package.bash'
[0.142s] INFO:colcon.colcon_core.shell:Creating package script '/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph/share/localization_using_area_graph/package.zsh'
[0.143s] Level 1:colcon.colcon_core.environment:create_file_with_runtime_dependencies(/home/<USER>/AGLoc_ws/src/install/localization_using_area_graph/share/colcon-core/packages/localization_using_area_graph)
[0.153s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:closing loop
[0.153s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:loop closed
[0.153s] DEBUG:colcon.colcon_parallel_executor.executor.parallel:run_until_complete finished with '1'
[0.153s] DEBUG:colcon.colcon_core.event_reactor:joining thread
[0.155s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.notify2': 'notify2' not found
[0.155s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.terminal_notifier': Not used on non-Darwin systems
[0.156s] INFO:colcon.colcon_core.plugin_system:Skipping extension 'colcon_notification.desktop_notification.win32': Not used on non-Windows systems
[0.156s] INFO:colcon.colcon_notification.desktop_notification:Sending desktop notification using 'notify_send'
[0.173s] DEBUG:colcon.colcon_core.event_reactor:joined thread
[0.173s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/AGLoc_ws/src/install/local_setup.ps1'
[0.174s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/AGLoc_ws/src/install/_local_setup_util_ps1.py'
[0.174s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/AGLoc_ws/src/install/setup.ps1'
[0.175s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/AGLoc_ws/src/install/local_setup.sh'
[0.176s] INFO:colcon.colcon_core.shell:Creating prefix util module '/home/<USER>/AGLoc_ws/src/install/_local_setup_util_sh.py'
[0.176s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/AGLoc_ws/src/install/setup.sh'
[0.176s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/AGLoc_ws/src/install/local_setup.bash'
[0.177s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/AGLoc_ws/src/install/setup.bash'
[0.177s] INFO:colcon.colcon_core.shell:Creating prefix script '/home/<USER>/AGLoc_ws/src/install/local_setup.zsh'
[0.177s] INFO:colcon.colcon_core.shell:Creating prefix chain script '/home/<USER>/AGLoc_ws/src/install/setup.zsh'
